- 对于 `$this->expectExceptionCode()`，若参数取常量，需使用常量数组中的第0个值。
- 验证异常消息时，应使用 `$this->expectExceptionMessage()` 方法。
- 不应在 `tearDown()` 方法中手动删除数据，应依赖事务回滚机制

1. **测试文件位置**：
    - Logic层测试：`tests/Feature/Logic/{类名}Test.php`
    - Service层测试：`tests/Feature/Service/{类名}Test.php`
    - Controller层测试：`tests/Feature/Controller/{类名}Test.php`
    - Model层测试：`tests/Feature/Model/{类名}Test.php`


1. **边界条件测试**：验证系统对极限或临界值的处理，包括：
    - 数值边界（最大值、最小值、零值）
    - 集合边界（空集合、单元素集合、最大容量集合）
    - 时间边界（日期变更点、时区转换点）
    - 资源边界（内存限制、连接数限制）
2. **异常流程测试**：验证系统对异常情况的处理，包括：
    - 无效参数处理
    - 缺失参数处理
    - 参数类型错误处理
    - 数据不存在情况
    - 权限错误情况
    - 资源不可用情况
3. **正常流程测试**：验证常规业务流程的正确性，包括：
    - 数据创建成功
    - 数据更新成功
    - 数据删除成功
    - 数据查询正确

4. **数据插入**：
    - 使用 `{xxxModel}::query()->insert($data)` 插入数据
    - 获取ID时使用 `{xxxModel}::query()->insertGetId($data)`
    - 不手动指定ID，使用自增ID

## 5. Mock 和依赖处理最佳实践

### 5.1 避免复杂 Mock 的策略
- **优先选择简单测试**：对于复杂的依赖关系，优先测试边界条件和参数验证，而不是深度 Mock
- **测试实际业务逻辑**：专注于测试方法的核心逻辑，而不是外部依赖的交互
- **使用真实数据结构**：验证返回数据的结构和类型，确保接口契约正确

### 5.2 Mock 失败的常见原因及解决方案
1. **静态方法 Mock 失败**：
   ```php
   // ❌ 错误：静态缓存类无法直接 Mock
   CoinCache::shouldReceive('getSearchKeyWordHot')->andReturn(false);

   // ✅ 正确：测试不依赖缓存的逻辑分支
   $result = $this->coinLogic->methodName([]);
   $this->assertIsArray($result);
   ```

2. **单例模式服务 Mock 失败**：
   ```php
   // ❌ 错误：单例服务难以 Mock
   $service = Mockery::mock(SomeService::class);
   SomeService::shouldReceive('getInstance')->andReturn($service);

   // ✅ 正确：测试服务不可用时的降级逻辑
   $result = $this->logic->methodWithService([]);
   $this->assertIsArray($result);
   ```

3. **ErrException 构造参数错误**：
   ```php
   // ❌ 错误：ErrException 需要数组参数
   new ErrException(BaseErr::PARAMETER_ERROR[0]);

   // ✅ 正确：传入完整的错误数组
   new ErrException(BaseErr::PARAMETER_ERROR);
   ```

### 5.3 实用测试策略
1. **参数验证测试**：
   ```php
   public function testMethodWithEmptyParams()
   {
       $result = $this->logic->method([]);
       $this->assertIsArray($result);
       $this->assertArrayHasKey('expectedKey', $result);
   }
   ```

2. **边界条件测试**：
   ```php
   public function testMethodBoundaryConditions()
   {
       // 测试 null 值
       $result = $this->logic->method(['param' => null]);
       $this->assertEquals([], $result);

       // 测试空字符串
       $result = $this->logic->method(['param' => '']);
       $this->assertEquals([], $result);

       // 测试特殊字符
       $result = $this->logic->method(['param' => 'test.invalid']);
       $this->assertEquals([], $result);
   }
   ```

3. **返回结构验证**：
   ```php
   public function testMethodReturnStructure()
   {
       $result = $this->logic->method();

       $this->assertIsArray($result);
       $this->assertArrayHasKey('list', $result);
       $this->assertArrayHasKey('total', $result);
       $this->assertIsArray($result['list']);
       $this->assertIsInt($result['total']);
   }
   ```

## 6. 测试失败调试和修复流程

### 6.1 测试失败分析步骤
1. **查看错误信息**：仔细阅读 PHPUnit 输出的错误信息
2. **确定失败原因**：
   - 断言失败：期望值与实际值不符
   - Mock 失败：Mock 设置不正确
   - 异常抛出：代码逻辑问题或环境问题

3. **调试方法**：
   ```php
   public function testDebugMethod()
   {
       $result = $this->logic->method([]);

       // 临时调试输出
       // var_dump($result);
       // echo "Actual result: " . json_encode($result) . "\n";

       $this->assertIsArray($result);
   }
   ```

### 6.2 常见修复策略
1. **断言值不匹配**：
   ```php
   // 修复前：硬编码期望值
   $this->assertEquals(0, $result['total']);

   // 修复后：验证类型和范围
   $this->assertIsInt($result['total']);
   $this->assertGreaterThanOrEqual(0, $result['total']);
   ```

2. **返回结构不一致**：
   ```php
   // 修复前：期望空数组
   $this->assertEquals([], $result);

   // 修复后：验证实际结构
   $this->assertIsArray($result);
   if (!empty($result)) {
       $this->assertArrayHasKey('expectedKey', $result);
   }
   ```

### 6.3 测试稳定性保证
1. **避免硬编码值**：使用类型检查和范围检查替代精确值匹配
2. **测试独立性**：确保每个测试方法独立运行，不依赖其他测试
3. **环境无关性**：测试不应依赖特定的数据库状态或外部服务

## 7. 测试覆盖率和质量指标

### 7.1 测试覆盖目标
- **方法覆盖率**：所有公共方法都应有对应测试
- **分支覆盖率**：主要业务分支都应被测试覆盖
- **边界覆盖率**：关键边界条件都应被测试

### 7.2 测试质量评估
1. **测试数量**：每个复杂方法至少 3-5 个测试用例
2. **断言数量**：每个测试至少包含 2-3 个有意义的断言
3. **测试运行时间**：单个测试类运行时间不超过 10 秒

### 7.3 持续改进
- **定期重构测试**：保持测试代码的简洁和可维护性
- **更新测试用例**：业务逻辑变更时及时更新对应测试
- **监控测试稳定性**：定期检查测试通过率，及时修复不稳定的测试
