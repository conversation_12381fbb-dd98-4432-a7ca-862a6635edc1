<?php

namespace App\Logic\Coin;

use App\CodisCache\Coin\CoinCache;
use App\CodisCache\Official\WebsiteCache;
use App\ConstDir\CoinVersionConst;
use App\ConstDir\YearsConst;
use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use App\Logic\BaseLogic;
use App\Repository\Coin\TrendCoinRepo;
use App\Repository\Official\CoinTradeOfficialRepo;
use App\Repository\Official\CoinVersionOfficialRepo;
use App\Repository\Price\PriceCateRepo;
use App\Services\Coin\CoinPriceService;
use App\Services\Coin\CoinSearchService;
use App\Services\Coin\CoinService;
use App\Services\Coin\CoinVersionService;
use App\Services\EsService;
use App\Services\Official\CoinTradeOfficialService;
use App\Services\Official\CoinVersionOfficialService;
use App\Services\PinyinService;
use App\Services\PriceToolService;
use App\Services\WordsService;
use Exception;
use Spin\Logger\Facades\Log;

class CoinLogic extends BaseLogic
{
    public function toolsIndexSearchKey(): array
    {
        // 热搜词
        $today = date('Ymd');
        $defaultKeyWords = CoinCache::getSearchKeyWordHot($today);
        $tradeNum = isTest() ? 0 : 500;
        if ($defaultKeyWords) {
            $defaultKeyWords = json_decode($defaultKeyWords, true);
        } else {
            $sampleVersion = PriceToolService::getInstance()->priceInfoGetInfoSampleList($tradeNum, 90);
            $outerNos = array_column($sampleVersion, 'uniqNo');
            $outerNos = array_merge($outerNos, YearsConst::TANG_YEARS);
            $outerNos = array_merge($outerNos, YearsConst::BEI_SONG_YEARS);
            $outerNos = array_values(array_unique($outerNos));

            //获取版别信息
            $versionList = CoinVersionService::getInstance()->batchGetVersionInfo($outerNos);
            $versionUriList = CoinVersionOfficialRepo::getInstance()->batchGetVersionInfo(['uri', 'outer_no'], $outerNos);
            $versionIndexUri = array_column($versionUriList, 'uri', 'outer_no');

            $defaultKeyWords = [];
            foreach ($versionList as $vs) {
                if (empty($versionIndexUri[$vs['outer_no']])) {
                    Log::error(__METHOD__, 'miss Uri', ['outerNo' => $vs['outer_no']]);

                    CoinVersionOfficialService::getInstance()->addUriTowardMiss($vs['outer_no']);
                    continue;
                }
                $defaultKeyWords[] = [
                    'years' => $vs['years'],
                    'faceVal' => $vs['face_val'],
                    'fonts' => $vs['fonts'],
                    'coinName' => $vs['coin_name'],
                    'coinNo' => $vs['coin_no'],
                    'versionName' => CoinVersionService::getInstance()->getValidVersionName($vs['version_name'] ?? '', isset($vs['show_name']) ? $vs['show_name'] : ''),
                    'outerNo' => $vs['outer_no'],
                    'images' => $vs['images'],
                    'uri' => $versionIndexUri[$vs['outer_no']]
                ];
            }

            CoinCache::setSearchKeyWordHot($today, wpt_json_encode($defaultKeyWords));
        }

        //热门版别
        $hotVersion = CoinCache::getSearchHotVersion();

        if (!$hotVersion && $defaultKeyWords) {
            $years = [];

            $hotVersion = [];

            foreach ($defaultKeyWords as $version) {

                if (!in_array($version['years'], $years)) {
                    $years[$version['years']][] = $version['years'];
                }

                //同一个朝代超过3个 跳过
                if (!empty($years[$version['years']]) && count($years[$version['years']]) > 3) {
                    continue;
                }

                $value = $version['coinName'] . ' ' . $version['versionName'];
                if (!empty($version['fonts'])) {
                    $value .= ' ' . $version['fonts'];
                }

                $recentPrice = CoinPriceService::getInstance()->getVersionPrice($version['outerNo']);
                if (empty($recentPrice)) {
                    continue;
                }
                $hotVersion[] = [
                    'value' => $value,
                    'outerNo' => $version['outerNo'],
                    'uri' => $version['uri'] ?? '',
                    'recentPrice' => $recentPrice
                ];
            }

            //打乱后 取前6个
            shuffle($hotVersion);
            $hotVersion = array_slice($hotVersion, 0, 6);
            $hotVersion = wpt_json_encode($hotVersion);
            //计入缓存
            CoinCache::setSearchHotVersion($hotVersion);
        }
        $hotVersion = json_decode($hotVersion, true);

        $hotYears = CoinVersionService::getInstance()->getHotYears($defaultKeyWords);

        return [
            'searchDefault' => CoinVersionConst::SEARCH_RECOMMEND,
            'hotVersion' => $hotVersion,
            'hotYears' => $hotYears
        ];
    }

    /**
     * 钱币
     * @return array
     */
    public function coinList(): array
    {
        $cacheKey = 'coinList';
        $cache = WebsiteCache::getSeoWebsiteNew($cacheKey);

        if (false !== $cache) {
            return json_decode($cache, true);
        }
        $pinyinService = PinyinService::getInstance()->getPinyinClient();
        $result = [];
        foreach (YearsConst::YEARS_LIST as $year) {
            $result[$year] = [
                'year' => $year,
                'uri' => $pinyinService->permalink($year, ''),
                'coins' => []
            ];
        }

        $columns = ['id', 'years', 'coin_name', 'front_image'];
        $coins = TrendCoinRepo::getInstance()->batchCoinByYears($columns, YearsConst::YEARS_LIST);
        $newColumns = ['uri', 'coin_name'];
        $newCoins = CoinVersionOfficialRepo::getInstance()->getList($newColumns, ['outer_no' => '', 'is_delete' => 0]);
        $indexCoins = array_column($newCoins, 'uri', 'coin_name');
        foreach ($coins as $coin) {
            if (empty($indexCoins[$coin['coin_name']])) {
//                Log::info(__METHOD__, 'miss coin uri', ['coinName' => $coin['coin_name']]);
                continue;
            }
            $coinYear = $coin['years'];
            $result[$coinYear]['coins'][] = [
                'id' => $coin['id'],
                'years' => $coin['years'],
                'name' => $coin['coin_name'],
                'image' => $coin['front_image'],
                'uri' => $indexCoins[$coin['coin_name']]
            ];
        }

        $data = [
            'item' => array_values($result)
        ];

        // todo 缓存数据
//        WebsiteCache::setSeoWebsiteNew($cacheKey, wpt_json_encode($data));

        return $data;
    }

    /**
     * 钱币新
     * @return array
     */
    public function coinListNew(): array
    {
        $cacheKey = 'coinListNew';
        $cache = WebsiteCache::getSeoWebsiteNew($cacheKey);

        if (false !== $cache) {
            return json_decode($cache, true);
        }

        $result = [];

        foreach (YearsConst::YEARS_NEW as $item) {
            $result[] = [
                'years' => $item['years'],
                'uri' => $item['uri'],
                'coins' => array_values($item['coins'])
            ];
        }

        $data = [
            'item' => array_values($result)
        ];

        // todo 缓存数据
//        WebsiteCache::setSeoWebsiteNew($cacheKey, wpt_json_encode($data));

        return $data;
    }

    /**
     * 版别列表
     * @param array $params
     * @return array
     */
    public function coinVersionList(array $params): array
    {
        $keyword = $params['keyword'] ?? '';
        $uri = $params['uri'] ?? '';
        $page = intval($params['page'] ?? 1);
        $pageSize = intval($params['pageSize'] ?? 10);
        $returnData = [
            'isEnd' => true,
            'list' => [],
            'total' => 0,
            'page' => $page + 1,
        ];

        // 如果keyword不为空判断是否都为汉字
        if (!empty($keyword) && !WordsService::getInstance()->isChinese($keyword)) {
            Log::error(__METHOD__, 'keyword is not chinese', [
                'keyword' => $keyword
            ]);
            return $returnData;
        }

        if (!empty($uri)) {
            $coinNo = CoinVersionOfficialService::getInstance()->getFieldsByUri($uri, 'coin_no');
            $coinResult = CoinVersionService::getInstance()->getCoinVersionList($coinNo, $page, $pageSize);
        } elseif (empty($keyword)) {
            $coinResult = CoinVersionService::getInstance()->getCoinVersionList('', $page, $pageSize);
        } else {
            $coinResult = $this->searchNew([
                'keyword' => $keyword,
                'page' => $page,
                'pageSize' => $pageSize
            ]);
        }

        $returnData['total'] = $coinResult['totalCount'] ?? 0;
        $returnData['page'] = $page + 1;
        $returnData['list'] = CoinVersionService::getInstance()->formatCoinVersionList($coinResult['data'] ?? [], $params);
        if (!empty($returnData['list']) && empty($keyword)) {
            $returnData['isEnd'] = false;
        }

        return $returnData;
    }

    /**
     * 钱币详情页
     * @param array $params
     * @return array
     */
    public function detail(array $params): array
    {
        $keyword = trim($params['keyword'] ?? '');
        $uri = $params['uri'] ?? '';
        if (empty($keyword) && empty($uri)) {
            return [];
        }

        if (!empty($uri)) {
            if (strpos($uri, '.')) {
                Log::info('coinDetail', 'uriError', [
                    'args' => func_get_args()
                ]);
                return [];
            }
            $outerNo = CoinVersionOfficialService::getInstance()->getFieldsByUri($uri, 'outer_no');
        } else {
            $outerNo = CoinSearchService::getInstance()->searchCoinVersionByKeyword($keyword);
        }

        if (empty($outerNo)) {
            Log::info('coinDetail', 'coinDetailMiss', [
                'args' => func_get_args()
            ]);
            return [];
        }

        try {
            $detail = $this->getDetailByOuterNo($outerNo);
        } catch (Exception $e) {
            return [];
        }

        $detail['graph'] = [];

        // 增加上一页下一页的数据
        $detail['pageItem'] = CoinVersionService::getInstance()->getCoinVersionPageItem($uri, $outerNo);

        return $detail;
    }

    /**
     * 推荐钱币
     * 推荐交易量大的钱币
     * @return array
     */
    public function recommend(): array
    {
        $cacheKey = 'recommend';
        $cache = WebsiteCache::getSeoWebsiteNew($cacheKey);
        if ($cache) {
            $cache = json_decode($cache, true);
            shuffle($cache);
            return [
                'list' => array_slice($cache, 0, 10)
            ];
        }

        $coinNos= PriceCateRepo::getInstance()->getRecommend(0, 200);
        $outerNos = CoinVersionOfficialRepo::getInstance()->batchGetMaxVersionByCoinId($coinNos);
        shuffle($outerNos);
        $outerNos = array_slice($outerNos, 0, 30);

        //获取版别信息
        $versionList = CoinVersionService::getInstance()->batchGetVersionInfo($outerNos);
        $versionUriList = CoinVersionOfficialRepo::getInstance()->batchGetVersionInfo(['uri', 'outer_no'], $outerNos);
        $versionIndexUri = array_column($versionUriList, 'uri', 'outer_no');

        $recommend = [];
        foreach ($versionList as $vs) {
            if (empty($versionIndexUri[$vs['outer_no']])) {
                Log::error('recommend', 'uri miss', ['outerNo' => $vs['outer_no']]);
                continue;
            }
            $recommend[] = [
                'years' => $vs['years'],
                'coinName' => $vs['coin_name'],
                'coinNo' => $vs['coin_no'],
                'versionName' => CoinVersionService::getInstance()->getValidVersionName($vs['version_name'], $vs['show_name'] ?? ''),
                'outerNo' => $vs['outer_no'],
                'images' => CoinVersionService::getInstance()->getCoinVersionImage($vs['images'], $vs['coin_name']),
                'uri' => $versionIndexUri[$vs['outer_no']]
            ];
        }

        WebsiteCache::setSeoWebsiteNew($cacheKey, wpt_json_encode($recommend));

        if (count($recommend) < 10) {
            Log::error('recommend', 'recommendNumberLessThan10', [
                'count' => count($recommend),
            ]);
        }

        return [
            'list' => array_slice($recommend, 0, 10)
        ];
    }

    /**
     * 更多版别
     * @param array $params
     * @return array
     */
    public function moreVersion(array $params)
    {
        $coinNo = $params['coinNo'] ?? '';
        $outerNo = $params['outerNo'] ?? '';
        $page = intval($params['page'] ?? 2);
        $pageSize = 5;

        if (empty($coinNo)) {
            return [];
        }

        $must[] = [
            'term' => ['state' => 1]
        ];

        $must[] = [
            'term' => [
                'coin_no' => $coinNo
            ]
        ];

        $query = [
            'bool' => [
                'must' => $must
            ]
        ];

        $sort = ['trade_num' => 'desc', 'id' => 'desc'];
        $from = ($page - 1) * $pageSize;
        $data = CoinSearchService::getInstance()->getCoinVersionFromEs($query, $sort, $pageSize, $from);

        $list = CoinVersionService::getInstance()->getHotCoinVersion($data['data'] ?? [], $outerNo);
        $totalPage = $page + 1;
        if (!empty($data['totalCount'])) {
            $totalPage = (int)ceil($data['totalCount'] / $pageSize);
        }
        return [
            'isEnd' => count($list) < 4, // 由于存在过滤当前版本的情况，判断小于4
            'totalPage' => $totalPage,
            'page' => $page + 1,
            'list' => $list
        ];
    }

    public function search(array $params)
    {
        $keyword = trim($params['keyword'] ?? '');

        $returnData = [
            'list' => [],
            'highlightKeywords' => [],
            'coinName' => '',
            'versionOuterNo' => ''
        ];
        if (empty($keyword)) {
            return $returnData;
        }

        //  先搜索币种
        $coinResult = CoinSearchService::getInstance()->searchCoinFromEs($keyword);

        $matchCoins = $coinResult['matchCoins'];

        //匹配到的币种数量
        $coinCount = count($matchCoins);

        //当搜索词中无币种信息，直接匹配版别（如当十）
        if (!$coinCount) {
            $version = CoinSearchService::getInstance()->searchVersionWithNoCoin($keyword);
            return CoinSearchService::getInstance()->formatReturn([], $version, $keyword);
        }

        //取版别数
        $size = $coinCount > 1 ? ceil(10 / $coinCount) : 100;

        //当搜索词仅能被币种全包含 但 搜索词不包含完整币种信息时，且还包含除币种外版别等信息时；（如 光绪宝新） 要显示币种信息
        if (!$coinResult['matchCoinName']) {
            return CoinSearchService::getInstance()->returnNoMatchCoin($coinResult, $keyword, $size);
        }

        //当搜索词被币种全包含  且 搜索词也完全包含币种信息，且还包含除币种外版别等信息时(如光绪通宝宝新)

        $version = [];
        foreach ($matchCoins as $mc) {
            $coinVersion = CoinSearchService::getInstance()->searchVersionByCoinNo($mc['outer_no'], $coinResult['withoutCoinNameKeyword'], $size);
            $version = array_merge($version, $coinVersion);
        }

        return CoinSearchService::getInstance()->formatReturn($coinResult, $version, $keyword);
    }

    /**
     * 获取交易记录
     * @param array $params
     * @return array
     */
    public function tradeRecord(array $params): array
    {
        $uri = $params['uri'] ?? '';
        $returnData = [
            'list' => []
        ];
        if (empty($uri)) {
            return $returnData;
        }

        $coinVersion = CoinVersionOfficialService::getInstance()->getOneByUri($uri, ['outer_no', 'coin_name']);
        if (empty($coinVersion)) {
            return $returnData;
        }
        $outerNo = $coinVersion['outer_no'];
        $coinCate = $coinVersion['coin_name'];

        // 获取最新交易记录数据
        $columns = ['title', 'version_outer_no', 'cover', 'trade_time', 'trade_price', 'coin_desc', 'score', 'comp', 'coin_cate'];
        $tradeRecord = CoinTradeOfficialRepo::getInstance()->getListByOuterNo($columns, $outerNo);

        if (empty($tradeRecord)) {
            $tradeRecord = CoinTradeOfficialRepo::getInstance()->getListByCoinCate($columns, $coinCate);
        }

        $returnData['list'] = CoinTradeOfficialService::getInstance()->formatTradeRecord($tradeRecord);

        return $returnData;
    }

    /**
     * 通过outerNo获取版别
     * @param string $outerNo
     * @return array
     * @throws ErrException
     */
    private function getDetailByOuterNo(string $outerNo): array
    {
        //获取版别信息
        $coinInfo = CoinVersionService::getInstance()->getCoinVersionByVersionId($outerNo);
        $versionInfo = $coinInfo['version'] ?? [];
        if (empty($versionInfo)) {
            throw new ErrException(BaseErr::QUERY_NOT_FOUND);
        }

        $coinName = $versionInfo['coin_name'];

        // 版别数据处理
        $versionInfo = CoinVersionService::getInstance()->handleVersionInfo($versionInfo);

        //组装统一格式
        $detail = CoinVersionService::getInstance()->formatDetailInfo($coinName, $versionInfo);

        //最新行情价
        $tradeSample = CoinPriceService::getInstance()->getCoinRecentPrice($outerNo);
        $detail['recentPrice'] = $tradeSample['recentPrice'] ?? [];

        // 版别数据
        $versionData = CoinVersionService::getInstance()->getCoinVersionListByCoinId($versionInfo['coin_id']);
        $detail['versionNums'] = $versionData['totalCount'] ?? 0;
        $versionList = $versionData['data'] ?? [];
        $detail['hotInfo'] = CoinVersionService::getInstance()->getHotCoinVersion($versionList, $outerNo);
        $detail['hotInfoMoreVersion'] = count($versionList) > 5;
        $versionSort = CoinVersionService::getInstance()->getVersionSort($versionList);
        $detail['commonVersion'] = $versionSort['commonVersion'];
        $detail['unCommonVersion'] = $versionSort['unCommonVersion'];
        $detail['coinRecentPrice'] = empty($versionData['coinRecentPrice']['coinNo']) ? [] : $versionData['coinRecentPrice'];

        $detail['sampleNum'] = $tradeSample['sampleNum'] ?? 0;

        $wikiInfo = [
            'content' => $coinInfo['coin_desc'] ?? ''
        ];
        $detail['wiki'] = $wikiInfo;

        return $detail;
    }

    /**
     * 推荐版别特性
     * @param array $params
     * @return array
     */
    public function recommendVersionList(array $params)
    {
        $returnData = [
            'list' => []
        ];
        $outerNo = $params['outerNo'] ?? '';

        // 获取版别相关信息
        $coinInfo = CoinVersionService::getInstance()->getCoinVersionByVersionId($outerNo);

        $version = $coinInfo['version'] ?? [];

        if (empty($version)) {
            return $returnData;
        }

        $years = $version['years'];
        $versionFeature = $version['version_feature'];
        // 版别特征为空，直接返回
        if (empty($versionFeature)) {
            return $returnData;
        }
        $versionFeature = explode(',', $versionFeature);
        $versionParams = [
            'coinId' => $version['coin_id'],
            'versionFeature' => $versionFeature,
        ];

        // 清朝、添加铸造局
        if ($years == '清') {
            $versionParams['foundry'] = $version['foundry'];
        }

        if (!empty($params['versionFeatureSelect'])) {
            unset($versionParams['versionFeature']);
            $versionParams['versionFeatureSelect'] = $params['versionFeatureSelect'];
        }

        $versionList = EsService::getInstance()->getRecommendVersionInfo($versionParams);

        $outerNos = array_column($versionList, 'outer_no');
        $indexOutNoUri = CoinVersionOfficialService::getInstance()->getIndexOuterNoUri($outerNos);

        $result = [];
        foreach ($versionList as $item) {
            $outerNo = $item['outer_no'];
            if (empty($indexOutNoUri[$outerNo])) {
                Log::error('getHotCoinVersion', 'uri reflection miss', [
                    'item' => $item,
                    'indexOutNoUri' => $indexOutNoUri
                ]);
                CoinVersionOfficialService::getInstance()->addUriTowardMiss($outerNo);
                continue;
            }

            // 等级处理
            $grade = '';
            if (!empty(CoinVersionConst::GRADE_SHOW_MAP[$item['grade'] ?? -1])) {
                $grade = CoinVersionConst::GRADE_SHOW_MAP[$item['grade'] ?? -1];
            }

            $result[] = [
                'uniqUri' => $item['outer_no'],
                'cateName' => $item['coin_name'],  // 币种名称
                'showName' => $item['show_name'], // 透出版别名称
                'years' => $item['years'],  // 朝代
                'diameter' => $item['diameter'],    // 直径
                'versionFeature' => $item['version_feature'],    // 版别特征
                'grade' => $grade,  // 等级
                'frontImg' => $item['front_img'],
                'backImg' => $item['back_img'],
                'uri' => $indexOutNoUri[$outerNo]
            ];
        }

        $returnData['list'] = $result;

        return $returnData;
    }

    /**
     * 搜索推荐
     * @param array $params
     * @return array
     */
    public function searchRecommend(array $params)
    {
        $data = $this->searchNew($params);

        $searchList = $data['data'] ?? [];
        // 获取outer_no为index的uri
        $outerNos = array_column($searchList, 'outer_no');
        $indexOutNoUri = CoinVersionOfficialService::getInstance()->getIndexOuterNoUri($outerNos);
        // 格式化前端需要
        $list = [];
        foreach ($searchList as $item) {
            if (empty($indexOutNoUri[$item['outer_no']])) {
                Log::error('formatCoinVersionList', 'uri reflection miss', [
                    'item' => $item,
                    'indexOutNoUri' => $indexOutNoUri,
                    'params' => $params
                ]);
                CoinVersionOfficialService::getInstance()->addUriTowardMiss($item['outer_no']);
                continue;
            }
            $list[] = [
                'coinName' => $item['coin_name'],
                'versionName' => CoinVersionService::getInstance()->getValidVersionName($item['version_name'], $item['show_name'] ?? ''),
                'outerNo' => $item['outer_no'],
                'faceVal' => $item['face_val'] ?? '',
                'fonts' => $item['fonts'] ?? '',
                'diameter' => $item['diameter'] ?? '',
                'uri' => $indexOutNoUri[$item['outer_no']],
            ];
        }

        return [
            'list' => $list
        ];
    }

    /**
     * 搜索核心
     * @param array $params
     * @return array
     */
    public function searchNew(array $params)
    {
        $keyword = trim($params['keyword'] ?? '');
        $page = $params['page'] ?? 1;
        $pageSize = $params['pageSize'] ?? 20;

        Log::info('searchNew', '搜索开始', [
            "args" => $params
        ]);

        try {
            $wordsArr = WordsService::getInstance()->cutWords($keyword);
        } catch (ErrException $e) {
            // 采用其他的搜索方式
            return EsService::getInstance()->defaultSearchVersion($keyword, $page, $pageSize);
        }

        // es搜索币种的信息
        $esRes = EsService::getInstance()->searchCoin($wordsArr);

        $coinNos = array_column($esRes, 'outer_no');

        // es搜索币种的信息
        return EsService::getInstance()->searchVersion($wordsArr, $coinNos, $page, $pageSize);
    }
}
